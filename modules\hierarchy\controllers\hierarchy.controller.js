const {
  SENT_SUCCESS,
  NOT_FOUND,
  FETCH,
  SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const {
  apiHandler,
  apiResponse,
  apiError,
  apiResponseV2,
} = require("../../../utils/api.util");
const { Hierarchy, ArazCity, HierarchyPosition, KGUser, Miqaat } = require("../models");
const {
  toObjectId,
  getUniqueName,
  toString,
  isEmpty,
  symmetricDifference,
  cleanKgUsers,
  generateOpenProjectGroupName,
} = require("../../../utils/misc.util");
const { PositionAssignment } = require("../models");
const { getPositionAssignment } = require("./positionAssignment.controller");
const constants = require("../../../constants");
const { createOpenProjectProject, createOpenProjectGroup, createOpenProjectMembership, getOpenProjectGroups, assignUserToGroup } = require("../../../utils/openProject");
const { clearCacheByPattern, redisCacheKeys, setCache, getCache } = require("../../../utils/redis.cache");
const { addEventLog, EventActions, Modules } = require("../../../utils/eventLogs.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
let allDepartments = [];
let arazCityGroups = [];
let arazCityGroupNames = []
let openProjectGroups = []

const addGroupsToOpenProject = async (groupNames) => {
  for (const [index, group] of groupNames.entries()) {
    const { groupName, projectIds, isExistingGroup, groupId, existingProjectIds } = group
    // console.log(`${index+1}/${groupNames.length}`, groupName, isExistingGroup)

    const groupData = {
      name: groupName
    }
    let id = groupId
    if(!isExistingGroup) {
      const createdGroup = await createOpenProjectGroup(groupData)
      id = createdGroup.id
    }
    const filteredProjectIds = symmetricDifference(projectIds, existingProjectIds)
    await createOpenProjectMembership({ groupID: id, groupName, projectIds: filteredProjectIds })

    arazCityGroups.push({
      id,
      name: groupName,
      projectIds,
    })
  }
}

const traverseNode = (node, arazCityData) => {
  const { assignedKGs, departmentID, departmentName, arazCityZoneID, arazCityZoneName, positionDetails: { originalName: hierarchyPositionName } } = node
  const miqaatName = arazCityData.miqaatData.name
  
  const departmentData = departmentID ? { _id: departmentID, name: departmentName } : null
  const arazCityZoneData = arazCityZoneID ? { _id: arazCityZoneID, name: arazCityZoneName } : null
  const [groupName, projectIds, isExistingGroup, groupId, existingProjectIds] = generateOpenProjectGroupName(
    arazCityData,
    { name: miqaatName },
    { name: hierarchyPositionName },
    departmentData,
    arazCityZoneData,
    openProjectGroups,
    true
  )
  if(groupName) {
    arazCityGroupNames.push({groupName, projectIds, isExistingGroup, groupId, existingProjectIds})
  }

  for(const child of node.children) {
    traverseNode(child, arazCityData)
  }
}

const generateHierarchy = async (miqaatID, arazCityID) => {

  const CheckMiqaat = await Miqaat.countDocuments({ _id: miqaatID, status: "active" })
  const CheckarazCity = await ArazCity.countDocuments({ _id: arazCityID, status: true })
  if(!CheckMiqaat || !CheckarazCity) return;
  
  await Hierarchy.findOneAndUpdate(
    {
      miqaatID,
      arazCityID,
    },
    { $set: { isGenerating: true } }
  )
  await clearCacheByPattern(`*`, miqaatID, arazCityID)
  
  allDepartments = [];
  const miqaat = await Miqaat.findById(miqaatID).select("name");

  let arazCity = await ArazCity.findById(arazCityID).populate([
    { path: "arazCityZones" },
    { path: "departments.departmentID" },
    {
      path: "hierarchyPositions.hierarchyPositionID",
      select: "_id name uniqueName isZonal isDepartmental isVisibleForArazCityUser parentType parents alias",
    },
  ]);
  
  if (!arazCity) {
    console.log(`Araz City not found - ${arazCityID}`);
    return;
  }

  if (arazCity.departments && arazCity.departments.length > 0) {
    arazCity.departments = arazCity.departments.filter(
      (dep) => dep.status === "active"
    );
  }
  
  let zones = arazCity.arazCityZones;
  let departments = arazCity.departments;
  
  if (arazCity?.hierarchyPositions) {
    arazCity.positions = arazCity.hierarchyPositions
    .filter((pos) => pos?.hierarchyPositionID)
    .map((pos) => ({
        ...pos.hierarchyPositionID.toObject(),
        weightage: pos.weightage,
        countRecommendation: pos.countRecommendation,
      }));
  } else {
    arazCity.positions = await HierarchyPosition.find({}).select(
      "_id name uniqueName isZonal isDepartmental isVisibleForArazCityUser parentType parents alias weightage countRecommendation"
    );
  }

  const sortedZones = zones.sort((a, b) => {
    const priorityA = a.priority ?? Infinity;
    const priorityB = b.priority ?? Infinity;

    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    return a.name.localeCompare(b.name);
  });

  const sortedDepartments = departments.sort((a, b) => {
    const priorityA =
      a.departmentID.priority && a.departmentID.priority !== 0
        ? a.departmentID.priority
        : Infinity;
    const priorityB =
      b.departmentID.priority && b.departmentID.priority !== 0
        ? b.departmentID.priority
        : Infinity;

    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    return a.departmentID.name.localeCompare(b.departmentID.name);
  });

  zones = sortedZones;
  departments = sortedDepartments;

  const positions = arazCity.positions;

  const userPipeline = [
    {
      $unwind: {
        path: "$miqaats",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.isActive": true,
      },
    },
    {
      $addFields: {
        miqaat: "$miqaats",
        mergedFields: { $mergeObjects: ["$$ROOT", { miqaat: "$miqaats" }] },
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaat.kgTypeID",
        foreignField: "_id",
        as: "KGType",
      },
    },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaat.kgGroupID",
        foreignField: "_id",
        as: "KGGroup",
      },
    },
    {
      $set: {
        KGType: { $arrayElemAt: ["$KGType.name", 0] },
        KGTypeColor: { $arrayElemAt: ["$KGType.color", 0] },
        KGTypePriority: { $arrayElemAt: ["$KGType.priority", 0] },
        KGGroup: { $arrayElemAt: ["$KGGroup.name", 0] },
      },
    },
    {
      $project: {
        _id: 0,
        ITSID: 1,
        name: 1,
        LDName: 1,
        logo: 1,
        email: 1, 
        phone: { $cond: { if: { $eq: ["$gender", "M"] }, then: "$phone", else: "$$REMOVE" } },
        whatsapp: { $cond: { if: { $eq: ["$gender", "M"] }, then: "$whatsapp", else: "$$REMOVE" } },
        gender:1,
        KGID: "$_id",
        KGType: 1,
        KGTypeColor: 1,
        KGTypePriority: 1,
        KGGroup: 1,
        KGTypeID: "$miqaat.kgTypeID",
        KGGroupID: "$miqaat.kgGroupID",
        departmentID: "$miqaat.departmentID",
        arazCityZoneID: "$miqaat.arazCityZoneID",
        hierarchyPositionID: "$miqaat.hierarchyPositionID",
        isInternationalPlugin: "$miqaat.isInternationalPlugin",
      },
    },
  ];
  const allUsers = await KGUser.aggregate(userPipeline);
  const users = allUsers.sort((a, b) => {
    const priorityA =
      a.KGTypePriority && a.KGTypePriority !== 0 ? a.KGTypePriority : Infinity;
    const priorityB =
      b.KGTypePriority && b.KGTypePriority !== 0 ? b.KGTypePriority : Infinity;

    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    return a.name.localeCompare(b.name);
  });

  const positionAssignments = await PositionAssignment.find({
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
  });

  const initialPosition = positions.find((pos) => pos._id.toString() === constants.HIERARCHY_POSITIONS.IPMO[0].toString());
  const departmentalHierarchy = await generateDepartmentalHierarchy(
    arazCity,
    miqaat,
    initialPosition,
    positions,
    positionAssignments,
    departments,
    zones,
    users
  );

  const zonalHierarchy = generateZonalHierarchy(
    arazCity,
    miqaat,
    initialPosition,
    positions,
    positionAssignments,
    departments,
    zones,
    users
  );
  
  const { userTree, departmentTree } = generateUserTreeHierarchy(
    arazCity,
    initialPosition,
    positions,
    departments,
    zones,
    users
  );
  const { totalWeightages, hierarchyWeightages, pendingPercentage } =
    calculateTotalWeightages(departmentalHierarchy);
  const hierarchyEntry = {
    isGenerating: false,
    miqaatID,
    arazCityID,
    departmental: departmentalHierarchy,
    zonal: zonalHierarchy,
    userTree,
    departmentTree,
    totalWeightages,
    hierarchyWeightages,
    pendingPercentage,
  };
  const hierarchy = await Hierarchy.findOneAndUpdate(
    { miqaatID: toObjectId(miqaatID), arazCityID: toObjectId(arazCityID) },
    { $set: hierarchyEntry },
    { new: true, upsert: true }
  );

  console.log(
    `Hierarchy added/updated succesfully ${hierarchy.createdAt.toLocaleTimeString()} ${hierarchy.updatedAt.toLocaleTimeString()}`
  );

  if(arazCity.addToOpenProject) {
    generateOpenProjectData(arazCityID)
  }

  // return {userTree, departmentTree, departmentalHierarchy, zonalHierarchy}
};

const calculateTotalWeightages = (hierarchy) => {
  // console.log("runn")
  let totalWeightages = 0;
  let hierarchyWeightages = 0;

  const calculateWeights = (node) => {
    // console.log(node.position, node.weightage, node.countRecommendation)
    const currentWeight = node.weightage * node.countRecommendation;
    // console.log(currentWeight)
    hierarchyWeightages += currentWeight;

    if (node.assignedKGs.length > node.countRecommendation) {
      totalWeightages += currentWeight;
    } else {
      totalWeightages +=
        currentWeight * (node.assignedKGs.length / node.countRecommendation);
    }

    node.children.forEach((child) => calculateWeights(child));
  };

  calculateWeights(hierarchy);

  const pendingPercentage =
    totalWeightages > 0
      ? ((totalWeightages - hierarchyWeightages) / hierarchyWeightages) * 100
      : 0;

  return {
    totalWeightages,
    hierarchyWeightages,
    pendingPercentage: Math.max(0, pendingPercentage),
  };
};

const generateZonalHierarchy = (
  arazCity,
  miqaat,
  position,
  positions,
  positionAssignments,
  departments,
  zones,
  users,
  cmzZone = false,
  zoneHeadNeed = true
) => {
  const isSingleDepartment = !Array.isArray(departments);
  const isSingleZone = !Array.isArray(zones);
  const zoneID = isSingleZone
    ? zones._id
      ? zones._id.toString()
      : null
    : null;
  const zoneName = isSingleZone
    ? zones.name
    : null;
  const departmentID = isSingleDepartment ? departments.departmentID : null;
  const departmentName = isSingleDepartment ? departments.departmentID.name : null;
  const positionData = positions.find(
    (pos) => pos.uniqueName === position.uniqueName
  );
  const { _id, isZonal, isDepartmental, parentType, uniqueName } = positionData;
  const positionID = _id.toString();
  let withoutCMZzones = [];
  let withCMZzone = [];

  let positionName = arazCity.showPositionAlias ? positionData.alias : positionData.name;
  if (isSingleZone) {
    positionName = `${zones.name} ${arazCity.showPositionAlias ? positionData.alias : positionData.name}`;
    withoutCMZzones = zones.uniqueName !== "cmz" ? zones : {};
    withCMZzone = zones.uniqueName === "cmz" ? zones : {};
  } else {
    withoutCMZzones = [...zones].filter((zone) => zone.uniqueName !== "cmz");
    withCMZzone = [...zones].filter((zone) => zone.uniqueName === "cmz");
  }
  if (isSingleDepartment) {
    positionName = `${zones.name} - ${departments.departmentID.name} ${arazCity.showPositionAlias ? positionData.alias : positionData.name}`;
  }

  const positionUsers = users.filter((user) => {
    let baseCondition =
      user?.hierarchyPositionID?.toString() === position?._id?.toString();

    if (isSingleZone) {
      baseCondition =
        baseCondition &&
        user?.arazCityZoneID?.toString() === zones?._id?.toString();
    }
    if (isSingleDepartment) {
      baseCondition =
        baseCondition &&
        user?.departmentID?.toString() ===
          departments.departmentID._id.toString();
    }
    if (baseCondition) return user;
  });

  const positionAssignment = getPositionAssignment(
    positionAssignments,
    positionID,
    zoneID,
    departmentID?._id
  );
  const countRecommendation = positionAssignment
    ? positionAssignment.countRecommendation
    : positionData.countRecommendation;
  const weightage = positionAssignment
    ? positionAssignment.weightage
    : positionData.weightage;

  const hierarchy = {
    isOpen: isSingleDepartment || isSingleZone ? false : true,
    arazCityZoneID: zoneID || null,
    arazCityZoneName: zoneName || null,
    departmentID,
    departmentName,
    position: positionName,
    positionID,
    positionDetails: {
      originalName: positionData.name,
      originalAlias: positionData.alias,
      parentType,
      isZonal,
      isDepartmental,
      isVisibleForArazCityUser: positionData.isVisibleForArazCityUser,
    },
    assignedKGs: positionUsers,
    count: positionUsers.length,
    countRecommendation,
    weightage,
    children: [],
  };
  if(isSingleDepartment) {
    hierarchy["departmentLDName"] = departments.departmentID.LDName;
    hierarchy["departmentName"] = departments.departmentID.name;
  }
  if(isSingleZone) {
    hierarchy["arazCityZoneLDName"] = zones.LDName;
    hierarchy["arazCityZoneName"] = zones.name;
  }

  const specialTeams = ["pmo_team", "hod_team", "zonal_chairman_team", "ipmo_team"];
  const childPositions = positions
  .filter(pos => toString(pos.parents).includes(positionID))
  .sort((a, b) => {
    const aIndex = specialTeams.indexOf(a.uniqueName);
    const bIndex = specialTeams.indexOf(b.uniqueName);

    if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;
    return 0;
  });

  // const childPositions = positions.filter((pos) => {
  //   if (toString(pos.parents).includes(positionID)) return pos;
  // });

  const pmoIndex = childPositions.findIndex(
    (item) => item.uniqueName === "pmo"
  );
  if (pmoIndex !== -1) {
    const [pmoItem] = childPositions.splice(pmoIndex, 1);
    childPositions.push(pmoItem);
  }

  // ------------Custom > CMZ Zonal Heirarchy
  if (uniqueName === "pmo") {
    const zoneHeadPosition = positions.find(
      (pos) => pos.uniqueName === "zone_head"
    );

    if (zoneHeadPosition) {
      const cmzZONE = withCMZzone[0];

      // ------------Filter users > CMZ Zone Head
      const cmzZoneHeadUsers = users.filter(
        (user) =>
          user?.hierarchyPositionID?.toString() ===
            zoneHeadPosition._id?.toString() &&
          user?.arazCityZoneID?.toString() === cmzZONE?._id?.toString()
      );

      const positionAssignment = getPositionAssignment(
        positionAssignments,
        positionID,
        zoneID,
        departmentID?._id
      );
      const countRecommendation = positionAssignment
        ? positionAssignment.countRecommendation
        : 1;
      const weightage = positionAssignment ? positionAssignment.weightage : 10;

      const pmoTeamPosition = positions.find(
        (pos) => pos.uniqueName === "pmo_team"
      );

      if(pmoTeamPosition) {
        const PMO_TEAM_HeadUsers = users.filter(
          (user) =>
            user?.hierarchyPositionID?.toString() ===
              pmoTeamPosition._id?.toString() 
              // && user?.arazCityZoneID?.toString() === cmzZONE?._id?.toString()
        );

        hierarchy.children.push({
          isOpen: false,
          arazCityZoneID: zoneID || null,
          arazCityZoneName: zoneName || null,
          departmentID,
          departmentName,
          position: pmoTeamPosition.name,
          positionID: pmoTeamPosition._id,
          positionDetails: {
            originalName: pmoTeamPosition.name,
            originalAlias: pmoTeamPosition.alias,
            parentType: pmoTeamPosition.parentType,
            isZonal: pmoTeamPosition.isZonal,
            isDepartmental: pmoTeamPosition.isDepartmental,
            isVisibleForArazCityUser: pmoTeamPosition.isVisibleForArazCityUser,
          },
          assignedKGs: PMO_TEAM_HeadUsers,
          count: PMO_TEAM_HeadUsers.length,
          countRecommendation,
          weightage,
          children: [],
        });
      }

      hierarchy.children.push({
        isOpen: false,
        arazCityZoneID: cmzZONE._id.toString() || null,
        arazCityZoneName: cmzZONE.name || null,
        departmentName,
        position: `${cmzZONE.name} - ${arazCity.showPositionAlias ? zoneHeadPosition.alias : zoneHeadPosition.name}`,
        positionID: zoneHeadPosition._id,
        positionDetails: {
          originalName: zoneHeadPosition.name,
          originalAlias: zoneHeadPosition.alias,
          parentType: zoneHeadPosition.parentType,
          isZonal: zoneHeadPosition.isZonal,
          isDepartmental: zoneHeadPosition.isDepartmental,
          isVisibleForArazCityUser: zoneHeadPosition.isVisibleForArazCityUser,
        },
        assignedKGs: cmzZoneHeadUsers,
        count: cmzZoneHeadUsers.length,
        countRecommendation,
        weightage,
        children: [],
      });

      // -----------Filter sub-positions of Zone Head
      const filteredZoneHeadSubPositions = positions.filter((pos) =>
        toString(pos.parents)?.includes(zoneHeadPosition._id.toString())
      );

      for (const subZoneHeadPosition of filteredZoneHeadSubPositions) {
        departments.forEach((department) => {
          const subDepartmentHierarchy = generateZonalHierarchy(
            arazCity,
            miqaat,
            subZoneHeadPosition,
            positions,
            positionAssignments,
            department,
            cmzZONE,
            users,
            true
          );
          hierarchy.children[1].children.push(subDepartmentHierarchy);
        });
      }
    }
  }

  for (const childPosition of childPositions) {
    if (childPosition.uniqueName === "hod" || childPosition.uniqueName === "hod_team" || childPosition.uniqueName === "pmo_team") continue;
    if (uniqueName === "pmo" && childPosition.uniqueName === "zone_head")
      continue;

    if (
      (childPosition.uniqueName === "zone_head" || isSingleZone) &&
      zoneHeadNeed
    ) {
      if (isSingleZone) {
        if (isSingleDepartment) {
          const subDepartmentHierarchy = generateZonalHierarchy(
            arazCity,
            miqaat,
            childPosition,
            positions,
            positionAssignments,
            departments,
            zones,
            users,
            cmzZone
          );

          hierarchy.children.push(subDepartmentHierarchy);
        } else {
          var departmentsData = departments;
          if (!cmzZone) {
            departmentsData = departmentsData.filter((dep) => dep.isZonal);
          }
          departmentsData.map((department) => {
            const subDepartmentHierarchy = generateZonalHierarchy(
              arazCity,
              miqaat,
              childPosition,
              positions,
              positionAssignments,
              department,
              zones,
              users,
              cmzZone
            );
            hierarchy.children.push(subDepartmentHierarchy);
          });
        }
      } else {
        withoutCMZzones.map((zone) => {
          const subZoneHierarchy = generateZonalHierarchy(
            arazCity,
            miqaat,
            childPosition,
            positions,
            positionAssignments,
            departments,
            zone,
            users,
            false
          );
          hierarchy.children.push(subZoneHierarchy);
        });
      }
    } else {
      const subHierarchy = generateZonalHierarchy(
        arazCity,
        miqaat,
        childPosition,
        positions,
        positionAssignments,
        departments,
        zones,
        users,
        cmzZone
      );

      hierarchy.children.push(subHierarchy);
    }
  }

  return hierarchy;
};

const generateDepartmentalHierarchy = async (
  arazCity,
  miqaat,
  position,
  positions,
  positionAssignments,
  departments,
  zones,
  users
) => {
  const isSingleDepartment = !Array.isArray(departments);
  const isSingleZone = !Array.isArray(zones);
  const zoneID = isSingleZone
    ? zones._id
      ? zones._id.toString()
      : null
    : null;
  const zoneName = isSingleZone
    ? zones.name
    : null;
  const departmentID = isSingleDepartment ? departments.departmentID : null;
  const departmentName = isSingleDepartment ? departments.departmentID.name : null;
  const positionData = positions.find(
    (pos) => pos.uniqueName === position.uniqueName
  );
  const { _id, isZonal, isDepartmental, parentType } = positionData;
  const positionID = _id.toString();

  let positionName = arazCity.showPositionAlias ? positionData.alias : positionData.name;

  if (isSingleDepartment) {
    positionName = `${arazCity.showPositionAlias ? positionData.alias : positionData.name} - ${departments.departmentID.name}`;
  }
  if (isSingleZone) {
    positionName = `${zones.name} - ${departments.departmentID.name} ${arazCity.showPositionAlias ? positionData.alias : positionData.name}`;
  }

  const positionUsers = users.filter((user) => {
    let baseCondition =
      user?.hierarchyPositionID?.toString() === position._id.toString();

    if (isSingleDepartment) {
      baseCondition =
        baseCondition &&
        user?.departmentID?.toString() ===
          departments.departmentID._id.toString();
    }
    if (isSingleZone) {
      baseCondition =
        baseCondition &&
        user?.arazCityZoneID?.toString() === zones._id.toString();
    }

    if (baseCondition) return user;
  });

  const positionAssignment = getPositionAssignment(
    positionAssignments,
    positionID,
    zoneID,
    departmentID?._id
  );
  const countRecommendation = positionAssignment
    ? positionAssignment.countRecommendation
    : positionData.countRecommendation;
  const weightage = positionAssignment
    ? positionAssignment.weightage
    : positionData.weightage;
  const hierarchy = {
    isOpen: isSingleDepartment || isSingleZone ? false : true,
    arazCityZoneID: zoneID || null,
    arazCityZoneName: zoneName || null,
    departmentID,
    departmentName,
    position: positionName,
    positionID,
    positionDetails: {
      originalName: positionData.name,
      originalAlias: positionData.alias,
      parentType,
      isZonal,
      isDepartmental,
      isVisibleForArazCityUser: positionData.isVisibleForArazCityUser,
    },
    assignedKGs: positionUsers,
    count: positionUsers.length,
    countRecommendation,
    weightage,
    children: [],
  };
  if(isSingleDepartment) {
    hierarchy["departmentLDName"] = departments.departmentID.LDName;
    hierarchy["departmentName"] = departments.departmentID.name; // Add this line
  }
  if(isSingleZone) {
    hierarchy["arazCityZoneLDName"] = zones.LDName;
    hierarchy["arazCityZoneName"] = zones.name; // Add this line
  }

  const specialTeams = ["pmo_team", "hod_team", "zonal_chairman_team", "ipmo_team"];

  const childPositions = positions
  .filter(pos => toString(pos.parents).includes(positionID))
  .sort((a, b) => {
    const aIndex = specialTeams.indexOf(a.uniqueName);
    const bIndex = specialTeams.indexOf(b.uniqueName);

    if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;
    return 0;
  });
  
  // const childPositions = positions.filter((pos) => {
  //   if (toString(pos.parents).includes(positionID)) return pos;
  // });

  const pmoIndex = childPositions.findIndex(
    (item) => item.uniqueName === "pmo"
  );
  if (pmoIndex !== -1) {
    const [pmoItem] = childPositions.splice(pmoIndex, 1);
    childPositions.push(pmoItem);
  }

  for (const childPosition of childPositions) {
    if (
      childPosition.uniqueName === "zonal_chairman" ||
      childPosition.uniqueName === "cmz_zone_head"
    )
      continue;
    if (childPosition.uniqueName === "hod" || isSingleDepartment) {
      if (isSingleDepartment) {
        if (isSingleZone) {
          const subZoneHierarchy = await generateDepartmentalHierarchy(
            arazCity,
            miqaat,
            childPosition,
            positions,
            positionAssignments,
            departments,
            zones,
            users
          );

          hierarchy.children.push(subZoneHierarchy);
        } else {
          if(childPosition.uniqueName === "hod_team") {
            const subHierarchy = await generateDepartmentalHierarchy(
              arazCity,
              miqaat,
              childPosition,
              positions,
              positionAssignments,
              departments,
              zones,
              users
            );
      
            hierarchy.children.push(subHierarchy);
          } else {
            for(const zone of zones) {
              const subZoneHierarchy = await generateDepartmentalHierarchy(
                arazCity,
                miqaat,
                childPosition,
                positions,
                positionAssignments,
                departments,
                zone,
                users
              );
    
              hierarchy.children.push(subZoneHierarchy);
            } 
          }
        }
      } else {
        for (const department of departments) {
          let zonesData = [];
          if (!department.isZonal) {
            zonesData = [...zones].filter((zone) => zone.uniqueName === "cmz");
          } else {
            zonesData = zones;
          }
          const subDepartmentHierarchy = await generateDepartmentalHierarchy(
            arazCity,
            miqaat,
            childPosition,
            positions,
            positionAssignments,
            department,
            zonesData,
            users
          );
  
          hierarchy.children.push(subDepartmentHierarchy);
        }
      }
    } else {
      const subHierarchy = await generateDepartmentalHierarchy(
        arazCity,
        miqaat,
        childPosition,
        positions,
        positionAssignments,
        departments,
        zones,
        users
      );

      hierarchy.children.push(subHierarchy);
    }
  }

  return hierarchy;
};

const generateUserTreeHierarchy = (
  arazCity,
  position,
  positions,
  departments,
  zones,
  users
) => {
  let positionData = {};
  let positionID = "";
  let positionName = "";
  let childPositions = [];
  let positionUsers = [];

  positionData = positions.find(
    (pos) => pos.uniqueName === position.uniqueName
  );

  positionID = positionData._id.toString();
  positionName = arazCity.showPositionAlias ? positionData.alias : positionData.name;

  childPositions = positions.filter((pos) => {
    if (toString(pos.parents).includes(positionID)) return pos;
  });

  positionUsers = users.filter((user) => {
    let baseCondition =
      user?.hierarchyPositionID?.toString() === position._id.toString();

    if (baseCondition) {
      user["position"] = positionName;
      user["KGTypeColor"] = user?.KGTypeColor || null;
      return user;
    }
  });
  if (!positionUsers.length) {
    positionUsers = [{ position: positionName }];
  }

  let userTree = {
    users: positionUsers,
    children: [],
  };

  for (let childPosition of childPositions) {
    if (childPosition.uniqueName === "zonal_chairman" || childPosition.uniqueName === "ipmo_team" || childPosition.uniqueName === "pmo_team" || childPosition.uniqueName === "lead_aqa_maula_utaro_team") {
      continue;
    } else if (childPosition.uniqueName === "hod") {
      departments.map((department) => {
        const departmentTree = {
          name: department.departmentID.name,
          LDName: department.departmentID.LDName,
          children: {},
        };

        const subDepartmentTree = generateDepartmentTreeHierarchy(
          arazCity,
          childPosition,
          positions,
          department,
          zones,
          users
        );

        departmentTree.children = subDepartmentTree;

        allDepartments.push(departmentTree);
      });
    } else {
      const { userTree: subUserTreeHierarchy } = generateUserTreeHierarchy(
        arazCity,
        childPosition,
        positions,
        departments,
        zones,
        users
      );

      userTree.children.push(subUserTreeHierarchy);
    }
  }
  return { userTree, departmentTree: allDepartments };
};

const generateDepartmentTreeHierarchy = (
  arazCity,
  position,
  positions,
  department,
  zones,
  users
) => {
  const positionData = positions.find(
    (pos) => pos.uniqueName === position.uniqueName
  );
  const positionID = positionData._id.toString();
  let positionUsers = [];

  let positionName = arazCity.showPositionAlias ? positionData.alias : positionData.name;
  positionName = `${department.departmentID.name} - ${arazCity.showPositionAlias ? positionData.alias : positionData.name}`;

  if (positionData.uniqueName === "zone_lead") {
    if(department.isZonal) {
      zones.map((zone) => {
        positionName = `${arazCity.showPositionAlias ? positionData.alias : positionData.name} - (${zone.name})`;
  
        for (const user of users) {
          user["KGTypeColor"] = user?.KGTypeColor || null;
          let baseCondition =
            user?.hierarchyPositionID?.toString() === position._id.toString() &&
            user?.departmentID?.toString() === department.departmentID._id.toString() &&
            user?.arazCityZoneID?.toString() === zone._id.toString() 
            // && (getUniqueName(user?.KGType) === constants.KG_TYPES.LOCAL_KG[1]);
  
          if (baseCondition) {
            user["position"] = positionName;
            zone.uniqueName === "cmz"
              ? positionUsers.unshift(user)
              : positionUsers.push(user);
          }
        }
  
        const foundUser = positionUsers.find((pUser) => 
          pUser.position.toLowerCase() === positionName.toLowerCase());
        if(!foundUser) {
          const data = { position: positionName }
          zone.uniqueName === "cmz"
            ? positionUsers.unshift(data)
            : positionUsers.push(data);
        }
      });
    }
  } else {
    users.map((user) => {
      user["KGTypeColor"] = user?.KGTypeColor || null;
      let baseCondition =
        user?.hierarchyPositionID?.toString() === position._id.toString() &&
        user?.departmentID?.toString() === department.departmentID._id.toString() 
        // && (getUniqueName(user?.KGType) === constants.KG_TYPES.LOCAL_KG[1]);

      if (baseCondition) {
        user["position"] = positionName;
        positionUsers.push(user);
      }
    });
  }
  if (!positionUsers.length) {
    if (positionData.uniqueName === "zone_lead") {
      if(department.isZonal) {
        zones.map((zone) => {
          const data = { position: `${arazCity.showPositionAlias ? positionData.alias : positionData.name} - (${zone.name})` };
          zone.uniqueName === "cmz"
            ? positionUsers.unshift(data)
            : positionUsers.push(data);
        });
      }
    } else {
      // console.log(positionData.uniqueName, department.isZonal)
      positionUsers.push({ position: positionName });
    }
  }

  const departmentTree = {
    users: positionUsers,
    children: {},
  };

  const childPositions = positions.filter((pos) => {
    if (toString(pos.parents).includes(positionID)) return pos;
  });

  for (const childPosition of childPositions) {
    if (
      childPosition.uniqueName === "zonal_chairman" ||
      childPosition.uniqueName === "zone_head" ||
      childPosition.uniqueName === "zone_team" ||
      childPosition.uniqueName === "hod_team" ||
      childPosition.uniqueName === "pmo_team" ||
      childPosition.uniqueName === "ipmo_team" ||
      childPosition.uniqueName === "lead_aqa_maula_utaro_team"
    ) {
      continue;
    } else {
      const subDepartmentTreeHierarchy = generateDepartmentTreeHierarchy(
        arazCity,
        childPosition,
        positions,
        department,
        zones,
        users
      );

      departmentTree.children = subDepartmentTreeHierarchy;
    }
  }

  return departmentTree;
};

const updateHierarchy = async (miqaatID, arazCityID, users, type) => {
  await clearCacheByPattern(`*`, miqaatID, arazCityID)
  const existingHierarchy = await Hierarchy.findOne({
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
  })

  switch (type) {
    case "add-edit":
      addEditUserInHierarchy(existingHierarchy, users)
      break;
    case "delete":
      deleteUserFromHierarchy(existingHierarchy, users)
      break;
    default:
      console.log("Invalid type")
      break;
  }

  const { hierarchyWeightages, totalWeightages, pendingPercentage } = 
    calculateTotalWeightages(existingHierarchy.departmental)
  existingHierarchy.totalWeightages = totalWeightages
  existingHierarchy.hierarchyWeightages = hierarchyWeightages
  existingHierarchy.pendingPercentage = pendingPercentage
  await existingHierarchy.save()

  console.log(`User ${type} succesfully`)
}

const addEditUserInHierarchy = (hierarchy, users) => {
  const { departmental, zonal, userTree, departmentTree } = hierarchy

  traverseAndAddEditUser(departmental, users)
  traverseAndAddEditUser(zonal, users)
  traverseAndAddEditTree(userTree, users)
  for(const department of departmentTree) {
    traverseAndAddEditTree(department.children, users, department)
  }
}

const deleteUserFromHierarchy = (hierarchy, users) => {
  const { departmental, zonal, userTree, departmentTree } = hierarchy

  traverseAndDeleteUser(departmental, users)
  traverseAndDeleteUser(zonal, users)
  traverseAndDeleteTree(userTree, users)
  for(const department of departmentTree) {
    traverseAndDeleteTree(department.children, users, true)
  }
}

const traverseAndAddEditUser = (node, users) => {
  const { positionID, arazCityZoneID, departmentID } = node
  
  const foundUsers = users.filter(user => {
    let baseCondition = user.hierarchyPositionID.toString() === positionID.toString()
    if(departmentID) {
      baseCondition = baseCondition && user.departmentID.toString() === departmentID.toString()
    }
    if(arazCityZoneID) {
      baseCondition = baseCondition && user.arazCityZoneID.toString() === arazCityZoneID.toString()
    }

    const foundIndex = node.assignedKGs.findIndex(assignedkg => 
      assignedkg.ITSID === user.ITSID &&
      (assignedkg.hierarchyPositionID.toString() !== user.hierarchyPositionID.toString() ||
      (assignedkg.departmentID && assignedkg.departmentID.toString() !== user?.departmentID.toString()) ||
      (assignedkg.arazCityZoneID && assignedkg.arazCityZoneID.toString() !== user?.arazCityZoneID?.toString()))
    )
    if(foundIndex !== -1) {
      node.assignedKGs.splice(foundIndex, 1)
      node.count = node.assignedKGs.length
    }

    if(baseCondition) {
      return user
    }
  })
  if(foundUsers.length) {

    const kgUsers = []
    node.assignedKGs.map(kg => {
      if(kgUsers.findIndex(user => user.ITSID === kg.ITSID) === -1) {
        kgUsers.push(kg)
      }
    })
    foundUsers.map(kg => {
      if(kgUsers.findIndex(user => user.ITSID === kg.ITSID) === -1) {
        kgUsers.push(kg)
      }
    })

    node.assignedKGs = kgUsers
    node.count = kgUsers.length
  }

  for(const child of node.children) {
    traverseAndAddEditUser(child, users)
  }
}

const traverseAndAddEditTree = (node, users, departmentData = null) => {
  const { users: nodeUsers, children } = node

  const foundUsers = users.map(user => {
    const { hierarchyPositionName, departmentName, arazCityZoneName } = user
    
    let positionName = hierarchyPositionName;
    if(departmentName) {
      positionName = `${departmentName} - ${hierarchyPositionName}`;
    }
    if(arazCityZoneName) {
      positionName = `${hierarchyPositionName} - (${arazCityZoneName})`;
    }

    const foundIndex = nodeUsers.findIndex(nodeUser => nodeUser.ITSID === user.ITSID)
    if(foundIndex !== -1) {
      nodeUsers.push({ position: nodeUsers[foundIndex].position })
      nodeUsers.splice(foundIndex, 1)
    }

    const foundPosition = nodeUsers.find(nodeUser => {
      let baseCondition = nodeUser.position?.toLowerCase() === positionName.toLowerCase()

      if(departmentData) {
        baseCondition = baseCondition && 
          user.departmentName.toLowerCase() === departmentData?.name.toLowerCase()
      }

      if(baseCondition) {
        return nodeUser
      }
    })
    if(foundPosition) {
      return {
        ...user,
        position: positionName
      }
    }

    return null
  })
  .filter(Boolean)
  
  const kgUsers = [...nodeUsers, ...foundUsers]
  const finalUsers = cleanKgUsers(kgUsers)

  node.users = finalUsers

  if(departmentData) {
    if(children?.users?.length > 0) {
      traverseAndAddEditTree(children, users, departmentData)
    }
  } else {
    for(const child of children) {
      traverseAndAddEditTree(child, users)
    }
  }
}

const traverseAndDeleteUser = (node, users) => {
  const userITSIDs = users.map(user => user.ITSID)
  const foundIndex = node.assignedKGs.findIndex(assignedkg => 
    userITSIDs.includes(assignedkg.ITSID)
  )
  if(foundIndex !== -1) {
    node.assignedKGs.splice(foundIndex, 1)
    node.count = node.assignedKGs.length
  }

  for(const child of node.children) {
    traverseAndDeleteUser(child, users)
  }
}

const traverseAndDeleteTree = (node, users, departmentData) => {
  const { users: nodeUsers, children } = node

  users.map(user => {
    const foundIndex = nodeUsers.findIndex(nodeUser => 
      nodeUser.ITSID === user.ITSID
    )
    if(foundIndex !== -1) {
      nodeUsers.push({ position: nodeUsers[foundIndex].position })
      nodeUsers.splice(foundIndex, 1)
    }
  })

  const kgUsers = [...nodeUsers]
  const finalUsers = cleanKgUsers(kgUsers)

  node.users = finalUsers

  if(departmentData) {
    if(children?.users?.length > 0) {
      traverseAndDeleteTree(children, users, departmentData)
    }
  } else {
    for(const child of children) {
      traverseAndDeleteTree(child, users)
    }
  }
}

const generateOpenProjectData = async (arazCityID) => {
  arazCityGroupNames = []
  arazCityGroups = []

  const existingHierarchy = await Hierarchy.findOne({
    arazCityID: toObjectId(arazCityID),
  }).select("departmental zonal")

  const pipeline = [
    {
      $match: {
        _id: toObjectId(arazCityID)
      }
    },
    {
      $project: {
        name: 1,
        addToOpenProject: 1,
        miqaatID: 1,
        arazCityZones: 1,
        hierarchyPositions: 1,
        departments: 1,
        openProjectGroups: 1,
        openProjectArazCityID: 1,
        openProjectMiqaatID: 1,
      }
    },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaatID",
        foreignField: "_id",
        as: "miqaatData"
      }
    },
    {
      $set: { miqaatData: { $arrayElemAt: ["$miqaatData", 0] } }
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "arazCityZones",
        foreignField: "_id",
        as: "arazCityZones"
      }
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "hierarchyPositions.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPositions"
      }
    },
    {
      $lookup: {
        from: "departments",
        localField: "departments.departmentID",
        foreignField: "_id",
        as: "departmentLookup"
      }
    },
    {
      $set: {
        departments: {
          $map: {
            input: "$departments",
            as: "dept",
            in: {
              $mergeObjects: [
                "$$dept",
                {
                  departmentData: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: "$departmentLookup",
                          as: "d",
                          cond: { $eq: ["$$d._id", "$$dept.departmentID"] }
                        }
                      },
                      0
                    ]
                  }
                }
              ]
            }
          }
        }
      }
    },
    {
      $unset: "departmentLookup"
    }
  ]
  const arazCityAggregate = await ArazCity.aggregate(pipeline)
  const arazCityData = arazCityAggregate[0]
  const { miqaatData, departments, arazCityZones } = arazCityData

  let openProjectArazCityID = arazCityData.openProjectArazCityID
  if(!openProjectArazCityID) {
    const openProjectArazCity = await createOpenProjectProject(arazCityData.name)
    if(!openProjectArazCity) {
      return
    }
    openProjectArazCityID = openProjectArazCity.id
  }

  let openProjectMiqaatID = arazCityData.openProjectMiqaatID
  if(!openProjectMiqaatID) {
    const openProjectMiqaat = await createOpenProjectProject(miqaatData.name, openProjectArazCityID)
    if(!openProjectMiqaat) {
      return
    }
    openProjectMiqaatID = openProjectMiqaat.id
  }

  const arazCityDepartments = []
  for (const department of departments) {
    let openProjectDepartmentID = department.openProjectDepartmentID
    if(!openProjectDepartmentID) {
      const openProjectDepartment = await createOpenProjectProject(department.departmentData.name, openProjectMiqaatID)
      if(!openProjectDepartment) {
        continue
      }
      openProjectDepartmentID = openProjectDepartment.id
    }

    // removed for chennai fasal city
    // for(const zone of arazCityZones) {
    //   if(zone.uniqueName !== "cmz" && !department.isZonal) {
    //     continue
    //   }

    //   let foundOpenProjectZone = department.arazCityZones.find(depZone => 
    //     depZone.arazCityZoneID.toString() === zone._id.toString()
    //   )
    //   if(!foundOpenProjectZone) {
    //     const openProjectZone = await createOpenProjectProject(zone.name, openProjectDepartmentID, department.departmentData.name)
    //     if(!openProjectZone) {
    //       continue
    //     }
    //     foundOpenProjectZone = {
    //       arazCityZoneID: zone._id,
    //       openProjectZoneID: openProjectZone.id
    //     }
    //     department.arazCityZones.push(foundOpenProjectZone)
    //   }
    // }

    const arazCityDepartmentData = {
      departmentID: department.departmentID,
      openProjectDepartmentID: openProjectDepartmentID,
      status: department.status,
      hierarchyPositions: department.hierarchyPositions,
      isZonal: department.isZonal,
      // arazCityZones: department.arazCityZones
    }

    arazCityDepartments.push(arazCityDepartmentData)
  }

  console.log("projects created on open project")

  openProjectGroups = await getOpenProjectGroups()

  traverseNode(existingHierarchy.departmental, arazCityData)
  traverseNode(existingHierarchy.zonal, arazCityData)

  arazCityGroupNames = arazCityGroupNames.filter(
    (obj, index, self) =>
      index === self.findIndex((o) => o.groupName === obj.groupName)
  )
  await addGroupsToOpenProject(arazCityGroupNames)
  
  await ArazCity.findByIdAndUpdate(
    arazCityID,
    {
      $set: {
        openProjectArazCityID,
        openProjectMiqaatID,
        departments: arazCityDepartments,
        openProjectGroups: [...arazCityGroups]
      }
    }
  )
  console.log("groups added to araz city")
}

const getHierarchy = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, type } = req.body;

  const cachekey = `${redisCacheKeys.HIERARCHY}:${miqaatID}:${arazCityID}:${type}`;

  const cachedHierarchy = await getCache(cachekey, miqaatID, arazCityID);
  if (cachedHierarchy) {
    const processedHierarchy = processHierarchyData(cachedHierarchy, req.allowFemalePhoto);
    return apiResponse(FETCH, "Hierarchy", processedHierarchy, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat or Araz City is not active", null, res);
  }

  const selectFields =
    type === "tree" ? "userTree departmentTree" : "departmental zonal";
  let existingHierarchy = await Hierarchy.findOne({
    miqaatID,
    arazCityID,
  }).select(`${selectFields} isGenerating`);
  
  if (!existingHierarchy) {
    return apiError(NOT_FOUND, "Hierarchy", null, res);
  }

  if(existingHierarchy?.isGenerating){
    return apiResponse(CUSTOM_SUCCESS, "Hierarchy generation is being processed.", { isGenerating: existingHierarchy?.isGenerating }, res);
  }

  // Process the hierarchy data to handle female photo visibility
  const processedHierarchy = processHierarchyData(existingHierarchy, req.allowFemalePhoto);
  
  apiResponse(SENT_SUCCESS, "Hierarchy", processedHierarchy, res);
  await setCache(cachekey, existingHierarchy, miqaatID, arazCityID);
});

// Helper function to process hierarchy data and remove female logos if needed
const processHierarchyData = (hierarchyData, allowFemalePhoto) => {
  if (allowFemalePhoto !== false) {
    return hierarchyData;
  }

  const processedData = JSON.parse(JSON.stringify(hierarchyData));

  const processNode = (node) => {
    if (!node || typeof node !== 'object') return node;

    // If this node has users array, process it
    if (node.users && Array.isArray(node.users)) {
      node.users = node.users.map(user => {
        if (user.gender === "F" && user.logo) {
          delete user.logo;
        }
        return user;
      });
    }

    // If this node has gender "F" and logo, remove logo
    if (node.gender === "F" && node.logo) {
      delete node.logo;
    }

    // Recursively process all nested objects and arrays
    Object.keys(node).forEach(key => {
      if (Array.isArray(node[key])) {
        node[key] = node[key].map(item => processNode(item));
      } else if (typeof node[key] === 'object' && node[key] !== null) {
        node[key] = processNode(node[key]);
      }
    });

    return node;
  };

  return processNode(processedData);
};


const getDashboardAnalytics = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  let queryObject = {};
  if (!isEmpty(miqaatID)) queryObject.miqaatID = miqaatID;
  if (!isEmpty(arazCityID)) queryObject.arazCityID = arazCityID;

  let hierarchies = await Hierarchy.find(queryObject)
    .populate("miqaatID", "name status")
    .populate("arazCityID", "name miqaatID status")
    .select(
      "miqaatID arazCityID hierarchyWeightages pendingPercentage totalWeightages"
    );

  hierarchies = hierarchies.filter((hierarchy) => {
    return hierarchy?.miqaatID?.status === "active" && hierarchy?.arazCityID?.status === true;
  });
  
  const groupedData = hierarchies.reduce((acc, hierarchy) => {
    const miqaat = acc.find(
      (group) => group.miqaatID === hierarchy?.miqaatID?._id
    );

    const cityData = {
      cityID: hierarchy?.arazCityID?._id,
      cityName: hierarchy?.arazCityID?.name,
      analytics: {
        totalWeightages: hierarchy?.totalWeightages,
        hierarchyWeightages: hierarchy?.hierarchyWeightages,
        percentage: (
          (hierarchy?.totalWeightages / hierarchy?.hierarchyWeightages) *
          100
        ).toFixed(2),
      },
    };

    if (miqaat) {
      miqaat.cities.push(cityData);
    } else {
      acc.push({
        miqaatID: hierarchy?.miqaatID?._id,
        name: hierarchy?.miqaatID?.name,
        cities: [cityData],
      });
    }

    return acc;
  }, []);

  return apiResponseV2(FETCH, "Dashboard Analytics", groupedData, res);
});

const getDashboardAliasName = apiHandler(async (req, res) => {
  const { arazCityID } = req.body;

  const cacheKey = `${redisCacheKeys.HIERARCHY}:DASHBOARD_ALIAS_NAME:${arazCityID}`
  const cachedData = await getCache(cacheKey);
  if (cachedData) {
    return apiResponse(FETCH, "Alias Name", cachedData, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(null, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCityData = await ArazCity.findById(arazCityID).select("showPositionAlias")
  if(!arazCityData) {
    return apiError(NOT_FOUND, "Araz City", null, res)
  }

  const hierarchyPositionIDs = [
    constants.HIERARCHY_POSITIONS.HOD[0],
    constants.HIERARCHY_POSITIONS.ZONE_HEAD[0],
  ]
  const hierarchyPositions = await HierarchyPosition
    .find({_id: {$in: toObjectId(hierarchyPositionIDs)}})
    .select("name alias")
    .sort({_id: 1})

  const response = {
    buttonNames: []
  }
  hierarchyPositions.forEach(position => {
    response.buttonNames.push(arazCityData.showPositionAlias ? position.alias : position.name)
  })
  await setCache(cacheKey, response);

  return apiResponse(FETCH, "Alias Name", response, res)
})

const regenerateHierarchy = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  await addEventLog(
    EventActions.UPDATE,
    Modules.Hierarchy,
    "Hierarchy",
    req.user._id
  );


  await generateHierarchy(miqaatID, arazCityID);
  return apiResponse(UPDATE_SUCCESS, "Hierarchy", null, res);
});

const editHierarchyUsers = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.body
  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const isAddToOpenProject = (await ArazCity.findById(arazCityID).select("addToOpenProject")).addToOpenProject
  if(!isAddToOpenProject) {
    return apiError(CUSTOM_ERROR, "Please enable add to open project toggle for the araz city", null, res)
  }

  const pipeline = [
    {
      $unwind: "$miqaats"
    },
    {
      $match: {
        // ITSID: {$in: []},
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.hierarchyPositionID": { $exists: true }
      }
    },
    {
      $sort: {
        createdAt: -1
      }
    },
    {
      $project: {
        name: 1,
        email: 1,
        ITSID: 1,
        miqaats: 1,
        openProjectID: 1
      }
    }
  ]
  const kgUsers = await KGUser.aggregate(pipeline)
  const validUsers = []
  const invalidUsers = []

  for(const [index, kguser] of kgUsers.entries()) {
    const { miqaats: { hierarchyPositionID, departmentID, arazCityZoneID } } = kguser
    try {
      await assignUserToGroup(kguser, arazCityID, miqaatID, hierarchyPositionID, departmentID, arazCityZoneID, true)
      validUsers.push({ name: kguser.name, ITSID: kguser.ITSID })
    } catch (error) {
      console.log(error)
      invalidUsers.push({ name: kguser.name, ITSID: kguser.ITSID, error: error.message })
    }
    console.log(`${index+1}/${kgUsers.length} kg users updated`)
  }

  const response = {
    invalidUsersCount: invalidUsers.length,
    validUsersCount: validUsers.length,
    invalidUsersITS: invalidUsers.map(user => user.ITSID),
    invalidUsers,
    validUsers
  }

  return apiResponse(CUSTOM_SUCCESS, `${validUsers.length}/${kgUsers.length} Users updated to open project successfully`, response, res)
})

module.exports = {
  generateHierarchy,
  updateHierarchy,
  getHierarchy,
  getDashboardAnalytics,
  getDashboardAliasName,
  regenerateHierarchy,
  editHierarchyUsers
};
